2025-05-24 21:22:20.015  2811-4714  JavaBinder              com.google.android.gms               E  !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
2025-05-24 21:22:20.048  4778-4778  GooglePartnerSetup      com.google.android.partnersetup      E  Phenotype client.register: true
2025-05-24 21:22:20.112  1935-2755  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@cab8acfb, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:20.308   323-411   BpfHandler              netd                                 E  Unsupported protocol: 1
2025-05-24 21:22:20.432  4481-4481  PbapClientService       com.google.android.bluetooth         E  Failed to register Authenication Service and get account visibility
2025-05-24 21:22:21.848  1522-4357  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 21:22:25.927  1043-1043  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-24 21:22:30.472  1522-4357  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 21:22:33.143  3244-3440  Finsky                  com.android.vending                  E  [229] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:33.144  3244-3440  Finsky                  com.android.vending                  E  [229] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:33.144  3244-3440  Finsky                  com.android.vending                  E  [229] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:33.198  3244-3485  Finsky                  com.android.vending                  E  [244] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:33.216  2067-4259  WorkSourceUtil          com.google.android.gms               E  Could not find package: com.google.android.gms.westworld
2025-05-24 21:22:34.309   621-778   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 21:22:34.920  3583-3721  Finsky                  com.android.vending                  E  [237] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:34.921  3583-3721  Finsky                  com.android.vending                  E  [237] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:34.922  3583-3721  Finsky                  com.android.vending                  E  [237] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:34.953  3583-3773  Finsky                  com.android.vending                  E  [250] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:38.270  3244-3485  Finsky                  com.android.vending                  E  [244] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:39.991  3583-3773  Finsky                  com.android.vending                  E  [250] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:43.366  4985-4985  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 21:22:43.800  3244-3424  Finsky                  com.android.vending                  E  [224] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:43.800  3244-3424  Finsky                  com.android.vending                  E  [224] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:43.801  3244-3424  Finsky                  com.android.vending                  E  [224] obb.a(333): SCH: Job 37-23 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:45.069  5025-5025  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 21:22:45.500  3583-3748  Finsky                  com.android.vending                  E  [242] iuw.a(52): Unexpected android-id = 0
2025-05-24 21:22:45.500  3583-3748  Finsky                  com.android.vending                  E  [242] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:45.501  3583-3748  Finsky                  com.android.vending                  E  [242] obb.a(333): SCH: Job 37-23 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:22:50.760  1522-4357  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 21:22:55.517  1522-4357  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 21:23:11.883  2067-2282  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 21:23:15.182  1522-2176  bnan                    com.google.android.gms.persistent    E  Phenotype registration failed [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@*********@24.26.32 (230800-*********):53)
                                                                                                    	at bnan.a(:com.google.android.gms@*********@24.26.32 (230800-*********):68)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@*********@24.26.32 (230800-*********):47)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@*********@24.26.32 (230800-*********):126)
                                                                                                    	at avkn.call(:com.google.android.gms@*********@24.26.32 (230800-*********):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 21:23:20.782  2811-3020  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 21:23:22.561  1522-4344  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!